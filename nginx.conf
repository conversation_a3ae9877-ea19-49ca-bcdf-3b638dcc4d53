server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;

    location / {
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

     # API 代理配置（根据实际接口地址修改）
    location ^~/api/ {
#         proxy_pass http://**************:8002/;  # 本地服务端地址
        proxy_pass http://**************:30006/entity-database/;  # k8s 服务地址

        # 将 /api/rule/app/page 转换为 /rule/app/page，匹配后端 Ingress 的路径规则
        # 添加路径重写规则，将 /api 转换为后端需要的 /rule
#         rewrite ^/api/(.*) /rule/$1 break;

        # 传递 Host 头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存配置
    location /static {
        expires 1y;
        add_header Cache-Control "public";
    }

    # 处理静态资源缓存
    #location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    #    expires 1y;
    #    add_header Cache-Control "public, no-transform";
    #}

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}
