"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FieldProperties = void 0;
var helpers_1 = require("./helpers");
exports.FieldProperties = [
    {
        key: 'visible',
        type: 'boolean',
        helpCode: helpers_1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    },
    { key: 'hidden', type: 'boolean', helpCode: helpers_1.<PERSON><PERSON><PERSON><PERSON><PERSON> },
    {
        key: 'display',
        type: '"visible" | "hidden" | "none"',
        helpCode: helpers_1.DisplayHelper,
    },
    {
        key: 'pattern',
        type: '"editable" | "disabled" | "readOnly" | "readPretty"',
        helpCode: helpers_1.Pat<PERSON>Helper,
    },
    { key: 'title', type: 'string', helpCode: helpers_1.StringHelper },
    { key: 'description', type: 'string', helpCode: helpers_1.StringHelper },
    { key: 'value', type: 'any', helpCode: helpers_1.<PERSON><PERSON><PERSON><PERSON> },
    { key: 'initialValue', type: 'any', helpCode: helpers_1.Any<PERSON><PERSON><PERSON> },
    { key: 'required', type: 'boolean', helpCode: helpers_1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> },
    {
        key: 'dataSource',
        type: 'Array<{label?:string,value?:any}>',
        helpCode: helpers_1.DataSourceHelper,
    },
    {
        key: 'componentProps',
        token: 'componentProps',
        type: 'object',
        helpCode: helpers_1.ComponentPropsHelper,
    },
    {
        key: 'decoratorProps',
        token: 'decoratorProps',
        type: 'object',
        helpCode: helpers_1.DecoratorPropsHelper,
    },
];
