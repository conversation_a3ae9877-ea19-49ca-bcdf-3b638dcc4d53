/// <reference types="react" />
import { TreeNode, Engine } from '@designable/core';
import { IDesignerLayoutContext, IWorkspaceContext, IDesignerComponents } from './types';
export declare const DesignerComponentsContext: import("react").Context<IDesignerComponents>;
export declare const DesignerLayoutContext: import("react").Context<IDesignerLayoutContext>;
export declare const DesignerEngineContext: import("react").Context<Engine>;
export declare const TreeNodeContext: import("react").Context<TreeNode>;
export declare const WorkspaceContext: import("react").Context<IWorkspaceContext>;
