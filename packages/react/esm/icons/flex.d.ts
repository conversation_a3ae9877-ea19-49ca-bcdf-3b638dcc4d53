import React from 'react';
export declare const FlexDirectionRow: React.JSX.Element;
export declare const FlexDirectionColumn: React.JSX.Element;
export declare const FlexAlignContentCenter: React.JSX.Element;
export declare const FlexAlignContentStart: React.JSX.Element;
export declare const FlexAlignContentEnd: React.JSX.Element;
export declare const FlexAlignContentSpaceAround: React.JSX.Element;
export declare const FlexAlignContentSpaceBetween: React.JSX.Element;
export declare const FlexAlignContentStretch: React.JSX.Element;
export declare const FlexJustifyCenter: React.JSX.Element;
export declare const FlexJustifyStart: React.JSX.Element;
export declare const FlexJustifyEnd: React.JSX.Element;
export declare const FlexJustifySpaceBetween: React.JSX.Element;
export declare const FlexJustifySpaceAround: React.JSX.Element;
export declare const FlexJustifySpaceEvenly: React.JSX.Element;
export declare const FlexAlignItemsCenter: React.JSX.Element;
export declare const FlexAlignItemsStart: React.JSX.Element;
export declare const FlexAlignItemsEnd: React.JSX.Element;
export declare const FlexAlignItemsStretch: React.JSX.Element;
export declare const FlexAlignItemsBaseline: React.JSX.Element;
export declare const FlexNoWrap: React.JSX.Element;
export declare const FlexWrap: React.JSX.Element;
