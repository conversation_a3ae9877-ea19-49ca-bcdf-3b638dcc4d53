import React from 'react';
export var AddSort = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("rect", { transform: "translate(370.000000, 495.274170) scale(1, -1) rotate(90.000000) translate(-370.000000, -495.274170) ", x: "-70", y: "455.27417", width: "880", height: "80", rx: "40" }),
    React.createElement("rect", { transform: "translate(313.431458, 838.637085) scale(-1, -1) rotate(45.000000) translate(-313.431458, -838.637085) ", x: "193.431458", y: "798.637085", width: "240", height: "80", rx: "40" }),
    React.createElement("rect", { transform: "translate(654.000000, 512.000000) scale(-1, 1) rotate(90.000000) translate(-654.000000, -512.000000) ", x: "214", y: "472", width: "880", height: "80", rx: "40" }),
    React.createElement("rect", { transform: "translate(710.568542, 168.637085) rotate(45.000000) translate(-710.568542, -168.637085) ", x: "590.568542", y: "128.637085", width: "240", height: "80", rx: "40" })));
export var AddIndex = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("path", { d: "M281,34 C195.672006,34 126.5,103.172006 126.5,188.5 C126.5,273.827994 195.672006,343 281,343 C366.327994,343 435.5,273.827994 435.5,188.5 C435.5,103.172006 366.327994,34 281,34 Z M281,114 C322.145214,114 355.5,147.354786 355.5,188.5 C355.5,229.645214 322.145214,263 281,263 C239.854786,263 206.5,229.645214 206.5,188.5 C206.5,147.354786 239.854786,114 281,114 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M739.612903,685 C654.284909,685 585.112903,754.172006 585.112903,839.5 C585.112903,924.827994 654.284909,994 739.612903,994 C824.940897,994 894.112903,924.827994 894.112903,839.5 C894.112903,754.172006 824.940897,685 739.612903,685 Z M739.612903,765 C780.758117,765 814.112903,798.354786 814.112903,839.5 C814.112903,880.645214 780.758117,914 739.612903,914 C698.467689,914 665.112903,880.645214 665.112903,839.5 C665.112903,798.354786 698.467689,765 739.612903,765 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M393.732359,149.064329 C505.758065,142.703248 593.848922,154.953101 658.388544,187.222912 C727.464102,221.760691 768.75285,279.025488 779.602138,355.372328 C790.479268,431.915099 771.469071,492.660327 720.942087,531.663613 C674.39496,567.594729 604.894755,581.798404 512.610685,577.088366 L510.128,576.956 L508.190557,576.875971 C440.452561,574.16688 391.4421,583.200136 361.247153,602.098655 L360.328695,602.68026 C333.829878,619.657857 321,645.428695 321,685 C321,720.96408 339.709031,746.580224 383.432217,766.281079 C434.006015,789.068691 513.641467,800.721303 621.377837,800.026361 L624.651176,800.001521 L625.348824,879.998479 C505.413438,881.044427 414.149806,867.867798 350.567783,839.218921 C279.624302,807.253109 241,754.369254 241,685 C241,619.237972 266.670122,567.675476 317.171305,535.31974 C363.286163,505.77429 427.932493,493.623563 511.200993,496.932429 L513.961445,497.047448 L514.192776,497.060149 C590.511632,501.250203 643.075213,490.708997 672.057913,468.336387 C697.530929,448.673006 707.520732,416.751568 700.397862,366.627672 C693.24715,316.307846 668.202565,281.572642 622.611456,258.777088 C572.985682,233.9642 499.070973,223.506553 401.23955,228.771319 L398.267641,228.935671 L393.732359,149.064329 Z", fillRule: "nonzero" })));
export var AddColumn = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("rect", { transform: "translate(354.000000, 512.000000) rotate(90.000000) translate(-354.000000, -512.000000) ", x: "-56", y: "472", width: "820", height: "80", rx: "40" }),
    React.createElement("path", { d: "M864,62 L160,62 C71.63444,62 0,133.63444 0,222 L0,802 C0,890.36556 71.63444,962 160,962 L864,962 C952.36556,962 1024,890.36556 1024,802 L1024,222 C1024,133.63444 952.36556,62 864,62 Z M160,142 L864,142 C908.18278,142 944,177.81722 944,222 L944,802 C944,846.18278 908.18278,882 864,882 L160,882 C115.81722,882 80,846.18278 80,802 L80,222 C80,177.81722 115.81722,142 160,142 Z", fillRule: "nonzero" }),
    React.createElement("rect", { transform: "translate(670.000000, 512.000000) rotate(90.000000) translate(-670.000000, -512.000000) ", x: "260", y: "472", width: "820", height: "80", rx: "40" })));
export var AddColumnGroup = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("path", { d: "M864,62 L160,62 C71.63444,62 0,133.63444 0,222 L0,802 C0,890.36556 71.63444,962 160,962 L864,962 C952.36556,962 1024,890.36556 1024,802 L1024,222 C1024,133.63444 952.36556,62 864,62 Z M160,142 L864,142 C908.18278,142 944,177.81722 944,222 L944,802 C944,846.18278 908.18278,882 864,882 L160,882 C115.81722,882 80,846.18278 80,802 L80,222 C80,177.81722 115.81722,142 160,142 Z", fillRule: "nonzero" }),
    React.createElement("rect", { x: "32", y: "472", width: "960", height: "80", rx: "40" }),
    React.createElement("rect", { transform: "translate(512.000000, 716.500000) rotate(90.000000) translate(-512.000000, -716.500000) ", x: "307.5", y: "676.5", width: "409", height: "80", rx: "40" })));
export var AddPanel = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("path", { d: "M864,62 L160,62 C71.63444,62 0,133.63444 0,222 L0,802 C0,890.36556 71.63444,962 160,962 L864,962 C952.36556,962 1024,890.36556 1024,802 L1024,222 C1024,133.63444 952.36556,62 864,62 Z M160,142 L864,142 C908.18278,142 944,177.81722 944,222 L944,802 C944,846.18278 908.18278,882 864,882 L160,882 C115.81722,882 80,846.18278 80,802 L80,222 C80,177.81722 115.81722,142 160,142 Z", fillRule: "nonzero" }),
    React.createElement("rect", { x: "40", y: "281", width: "984", height: "80", rx: "40" })));
export var AddOperation = (React.createElement("g", { stroke: "none", strokeWidth: "1", fillRule: "evenodd" },
    React.createElement("path", { d: "M421.97834,146.878887 C449.098125,146.878887 475.034958,155.079747 495.071944,170.058344 C517.366493,186.724577 531.199744,210.946101 531.474179,238.012376 L531.47834,238.833432 L531.47834,567.878887 C531.47834,589.970277 513.56973,607.878887 491.47834,607.878887 C469.607864,607.878887 451.83693,590.326658 451.483699,568.54036 L451.47834,567.878887 L451.47762,238.832254 L451.424408,238.717723 L451.319172,238.517315 L451.202904,238.308109 C450.531417,237.121323 449.203347,235.651792 447.172579,234.133697 C441.310242,229.751322 432.225599,226.878887 421.97834,226.878887 C411.73108,226.878887 402.646438,229.751322 396.784101,234.133697 C394.753333,235.651792 393.425263,237.121323 392.753776,238.308109 L392.559374,238.664382 L392.47834,238.833432 L392.47834,567.878887 C392.47834,589.970277 374.56973,607.878887 352.47834,607.878887 C330.607864,607.878887 312.83693,590.326658 312.483699,568.54036 L312.47834,567.878887 L312.47834,238.833432 C312.47834,211.436195 326.364989,186.892923 348.884736,170.058344 C368.921722,155.079747 394.858555,146.878887 421.97834,146.878887 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M699.97834,363.356159 C759.848768,363.356159 808.496698,411.405385 809.46367,471.045377 L809.47834,472.856159 L809.47834,567.878887 L729.47834,567.878887 L729.47834,472.856159 C729.47834,456.563759 716.27074,443.356159 699.97834,443.356159 C683.848864,443.356159 670.7428,456.300928 670.482292,472.368323 L670.47834,472.856159 L670.47834,567.878887 L590.47834,567.878887 L590.47834,472.856159 C590.47834,412.380979 639.50316,363.356159 699.97834,363.356159 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M832.43132,430.773744 C888.722088,430.773744 934.461351,475.9501 935.370508,532.024208 L935.3843,533.726724 L935.3843,567.878887 C935.3843,589.970277 917.47569,607.878887 895.3843,607.878887 C873.513824,607.878887 855.742891,590.326658 855.389659,568.54036 L855.3843,567.878887 L855.3843,533.726724 C855.3843,521.050143 845.107901,510.773744 832.43132,510.773744 C819.881505,510.773744 809.684108,520.845643 809.481415,533.347154 L809.47834,533.726724 L809.47834,567.878887 C809.47834,589.970277 791.56973,607.878887 769.47834,607.878887 C747.607864,607.878887 729.83693,590.326658 729.483699,568.54036 L729.47834,567.878887 L729.47834,533.726724 C729.47834,476.867363 775.571959,430.773744 832.43132,430.773744 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M560.97834,279.163422 C620.848768,279.163422 669.496698,327.212648 670.46367,386.852639 L670.47834,388.663422 L670.47834,567.878887 C670.47834,589.970277 652.56973,607.878887 630.47834,607.878887 C608.607864,607.878887 590.83693,590.326658 590.483699,568.54036 L590.47834,567.878887 L590.47834,388.663422 C590.47834,372.371022 577.27074,359.163422 560.97834,359.163422 C544.848864,359.163422 531.7428,372.10819 531.482292,388.175585 L531.47834,388.663422 L531.47834,567.878887 C531.47834,589.970277 513.56973,607.878887 491.47834,607.878887 C469.607864,607.878887 451.83693,590.326658 451.483699,568.54036 L451.47834,567.878887 L451.47834,388.663422 C451.47834,328.188242 500.50316,279.163422 560.97834,279.163422 Z", fillRule: "nonzero" }),
    React.createElement("path", { d: "M577.326586,260.358513 C632.767553,240.933771 694.424586,258.909478 730.740134,305.085234 C824.066269,423.750711 874.61782,534.886747 880.966628,638.993166 C887.483675,745.85833 847.257108,842.899322 762.327561,927.828869 C671.364292,1018.79214 572.753664,1061.66055 468.28526,1053.62452 C365.843318,1045.74437 261.475519,989.153266 154.690995,885.869874 C138.811879,870.511385 138.389828,845.188303 153.748317,829.309187 C169.106806,813.430071 194.429889,813.008019 210.309005,828.366509 C304.814212,919.773184 393.015159,967.598178 474.42098,973.860164 C553.800339,979.966268 630.316491,946.702854 705.759018,871.260327 C775.074298,801.945047 806.179407,726.908303 801.114976,643.862825 C795.882306,558.058601 751.939226,461.451087 667.85757,354.540094 C652.68919,335.253275 626.936111,327.745133 603.779371,335.858514 C588.359483,341.261149 579.811803,357.691411 584.168374,373.380386 L584.304347,373.855571 L611.89921,467.510864 C618.142899,488.701566 606.025951,510.941535 584.835249,517.185224 C563.856454,523.366476 541.849273,511.552468 535.352982,490.754252 L535.16089,490.121262 L507.566026,396.465969 C490.857458,339.758101 521.533808,279.906517 577.326586,260.358513 Z", fillRule: "nonzero", transform: "translate(512.074373, 653.600003) scale(-1, 1) rotate(45.000000) translate(-512.074373, -653.600003) " }),
    React.createElement("path", { d: "M416,26.3788866 C531.427513,26.3788866 625,119.951374 625,235.378887 C625,257.470277 607.09139,275.378887 585,275.378887 C563.129524,275.378887 545.35859,257.826658 545.005359,236.04036 L545,235.378887 C545,164.134154 487.244733,106.378887 416,106.378887 C345.467715,106.378887 288.156454,162.984824 287.017282,233.245636 L287,235.378887 C287,257.470277 269.09139,275.378887 247,275.378887 C224.90861,275.378887 207,257.470277 207,235.378887 C207,119.951374 300.572487,26.3788866 416,26.3788866 Z", fillRule: "nonzero" })));
