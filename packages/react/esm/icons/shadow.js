import React from 'react';
export var Blur = (React.createElement("path", { d: "M505.6 1024C308.842667 1024 149.333333 864.490667 149.333333 667.733333c0-67.2 36.288-159.317333 100.629334-274.069333a2317.653333 2317.653333 0 0 1 62.784-104.896 3431.210667 3431.210667 0 0 1 109.162666-161.621333 3361.28 3361.28 0 0 1 49.834667-67.776L505.6 15.189333l33.856 44.181334 3.712 4.885333 10.133333 13.525333c10.88 14.613333 22.976 31.168 35.989334 49.365334 37.205333 51.946667 74.389333 106.666667 109.162666 161.621333a2317.653333 2317.653333 0 0 1 62.784 104.896C825.578667 508.416 861.866667 600.533333 861.866667 667.733333c0 196.757333-159.509333 356.266667-356.266667 356.266667z m-14.293333-847.189333a3347.328 3347.328 0 0 0-106.453334 157.589333 2233.365333 2233.365333 0 0 0-60.458666 100.992C266.944 537.856 234.666667 619.797333 234.666667 667.733333 234.666667 817.365333 355.968 938.666667 505.6 938.666667c149.632 0 270.933333-121.301333 270.933333-270.933334 0-47.936-32.277333-129.877333-89.728-232.341333-18.112-32.298667-38.4-66.090667-60.458666-100.992a3347.328 3347.328 0 0 0-120.746667-177.429333c-4.629333 6.357333-9.386667 12.992-14.293333 19.84zM320 661.333333h85.333333a106.666667 106.666667 0 0 0 106.666667 106.666667v85.333333a192 192 0 0 1-192-192z", "p-id": "1221" }));
export var Shadow = (React.createElement("path", { d: "M672.761692,864 C694.848525,864 712.737861,881.9 712.737861,904 C712.737861,926.1 694.848525,944 672.761692,944 L632.785523,944 C610.698689,944 592.809354,926.1 592.809354,904 C592.809354,881.9 610.698689,864 632.785523,864 L672.761692,864 Z M512.857015,864 C534.943849,864 552.833184,881.9 552.833184,904 C552.833184,926.1 534.943849,944 512.857015,944 L472.880846,944 C450.794013,944 432.904677,926.1 432.904677,904 C432.904677,881.9 450.794013,864 472.880846,864 L512.857015,864 Z M352.952338,864 C375.039172,864 392.928508,881.9 392.928508,904 C392.928508,926.1 375.039172,944 352.952338,944 L312.976169,944 C290.889336,944 273,926.1 273,904 C273,881.9 290.889336,864 312.976169,864 L352.952338,864 Z M867.945338,878.6 C876.640155,898.9 867.245755,922.4 846.957849,931.1 C830.56762,938.1 813.177986,942.3 795.388591,943.6 C794.489127,943.7 793.589663,943.7 792.590259,943.7 C771.802651,943.7 754.213137,927.6 752.71403,906.5 C751.114984,884.5 767.705094,865.3 789.791927,863.8 C798.686625,863.2 807.381442,861.1 815.476616,857.6 C835.764522,848.9 859.250521,858.3 867.945338,878.6 Z M904.023831,724 C926.110664,724 944,741.9 944,764 L944,784 C944,792.9 943.300417,801.8 941.801311,810.5 C938.503277,830 921.613345,843.9 902.424784,843.9 C900.226095,843.9 898.027405,843.7 895.828716,843.3 C874.041704,839.7 859.350462,819.1 862.948317,797.3 C863.6479,792.9 864.047662,788.5 864.047662,784 L864.047662,764 C864.047662,741.9 881.936997,724 904.023831,724 Z M660,100 C726.27417,100 780,153.72583 780,220 L780,660 C780,726.27417 726.27417,780 660,780 L220,780 C153.72583,780 100,726.27417 100,660 L100,220 C100,153.72583 153.72583,100 220,100 L660,100 Z M620,180 L260,180 C216.259048,180 180.717181,215.104457 180.010718,258.677054 L180,260 L180,620 C180,663.740952 215.104457,699.282819 258.677054,699.989282 L260,700 L620,700 C663.740952,700 699.282819,664.895543 699.989282,621.322946 L700,620 L700,260 C700,215.81722 664.18278,180 620,180 Z M904.023831,564 C926.110664,564 944,581.9 944,604 L944,644 C944,666.1 926.110664,684 904.023831,684 C881.936997,684 864.047662,666.1 864.047662,644 L864.047662,604 C864.047662,581.9 881.936997,564 904.023831,564 Z M904.023831,404 C926.110664,404 944,421.9 944,444 L944,484 C944,506.1 926.110664,524 904.023831,524 C881.936997,524 864.047662,506.1 864.047662,484 L864.047662,444 C864.047662,421.9 881.936997,404 904.023831,404 Z M904.023831,244 C926.110664,244 944,261.9 944,284 L944,324 C944,346.1 926.110664,364 904.023831,364 C881.936997,364 864.047662,346.1 864.047662,324 L864.047662,284 C864.047662,261.9 881.936997,244 904.023831,244 Z" }));
export var AxisX = (React.createElement("path", { d: "M832,364 C898.27417,364 952,417.72583 952,484 L952,844 C952,910.27417 898.27417,964 832,964 L192,964 C125.72583,964 72,910.27417 72,844 L72,484 C72,417.72583 125.72583,364 192,364 L832,364 Z M792,444 L232,444 C188.259048,444 152.717181,479.104457 152.010718,522.677054 L152,524 L152,804 C152,847.740952 187.104457,883.282819 230.677054,883.989282 L232,884 L792,884 C835.740952,884 871.282819,848.895543 871.989282,805.322946 L872,804 L872,524 C872,479.81722 836.18278,444 792,444 Z M827.755788,28.2842712 L940.892873,141.421356 C956.513844,157.042328 956.513844,182.368927 940.892873,197.989899 L827.755788,311.126984 C812.134816,326.747955 786.808217,326.747955 771.187245,311.126984 C755.566273,295.506012 755.566273,270.179413 771.187245,254.558441 L811.773,213.972 L112,213.972961 C89.90861,213.972961 72,196.064351 72,173.972961 C72,151.881571 89.90861,133.972961 112,133.972961 L820.307,133.972 L771.187245,84.8528137 C755.566273,69.2318421 755.566273,43.9052429 771.187245,28.2842712 C786.808217,12.6632996 812.134816,12.6632996 827.755788,28.2842712 Z" }));
export var AxisY = (React.createElement("path", { d: "M820.027039,63 C842.118429,63 860.027039,80.90861 860.027039,103 L860.028,811.307 L909.147186,762.187245 C924.768158,746.566273 950.094757,746.566273 965.715729,762.187245 C981.3367,777.808217 981.3367,803.134816 965.715729,818.755788 L852.578644,931.892873 C836.957672,947.513844 811.631073,947.513844 796.010101,931.892873 L682.873016,818.755788 C667.252045,803.134816 667.252045,777.808217 682.873016,762.187245 C698.493988,746.566273 723.820587,746.566273 739.441559,762.187245 L780.028,802.773 L780.027039,103 C780.027039,80.90861 797.935649,63 820.027039,63 Z M510,63 C576.27417,63 630,116.72583 630,183 L630,823 C630,889.27417 576.27417,943 510,943 L150,943 C83.72583,943 30,889.27417 30,823 L30,183 C30,116.72583 83.72583,63 150,63 L510,63 Z M190,143 C146.259048,143 110.717181,178.104457 110.010718,221.677054 L110,223 L110,783 C110,826.740952 145.104457,862.282819 188.677054,862.989282 L190,863 L470,863 C514.18278,863 550,827.18278 550,783 L550,223 C550,179.259048 514.895543,143.717181 471.322946,143.010718 L470,143 L190,143 Z", transform: "translate(503.715729, 503.304301) scale(-1, 1) translate(-503.715729, -503.304301) " }));
