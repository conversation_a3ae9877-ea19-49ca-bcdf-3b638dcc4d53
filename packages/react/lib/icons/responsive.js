"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Responsive = void 0;
var react_1 = __importDefault(require("react"));
exports.Responsive = (react_1.default.createElement("svg", { viewBox: "0 0 1200 1024" },
    react_1.default.createElement("path", { d: "M420.444699 238.915368h320.727579c40.96 0 71.141053 17.785263 89.249684 54.541474 6.790737 13.635368 9.162105 28.456421 8.892632 43.870316-0.323368 85.692632 0 171.924211 0 257.616842v138.725053c0 57.775158-40.043789 98.088421-98.088421 98.088421-214.069895 0.269474-428.085895 0-641.778527 0-43.600842 0-78.848-23.390316-93.07621-61.386106a101.645474 101.645474 0 0 1-6.25179-35.247157c0-132.796632-0.269474-265.593263 0-398.389895 0-56.589474 41.822316-97.818947 99.004632-97.818948h321.320421z m0.592842 542.72c107.034947 0 214.339368-0.269474 321.320421 0.323369a47.481263 47.481263 0 0 0 49.25979-49.205895c-0.323368-132.203789-0.323368-264.084211-0.323369-396.341895 0-30.504421-18.701474-49.475368-49.529263-49.475368H99.178173c-29.911579 0-48.882526 18.378105-48.882527 48.343579-0.323368 132.473263 0 265.269895 0 397.743158 0 7.706947 0.538947 15.144421 4.149895 22.258526 9.485474 18.378105 24.576 26.677895 45.325474 26.677895 107.034947-0.269474 214.339368-0.269474 321.320421-0.269474z", "p-id": "2492" }),
    react_1.default.createElement("path", { d: "M1185.803857 474.866526v380.01179c0 26.677895-8.623158 49.475368-31.097263 65.536a72.650105 72.650105 0 0 1-43.870316 13.635368c-69.093053 0.269474-138.132211 0-207.494737 0-10.671158 0-14.821053-4.473263-15.090526-15.144421a221.884632 221.884632 0 0 1 0-19.887158c0.269474-10.078316 4.688842-14.497684 15.090526-14.767158 14.228211-0.323368 28.133053 0 42.361263 0h161.253053c20.156632 0 28.779789-8.623158 28.779789-28.779789 0-34.654316-0.323368-69.362526 0.269474-104.016842 0-5.928421-2.048-7.437474-7.706947-7.437474-74.374737 0.269474-148.48 0.269474-222.908632 0.269474-15.090526 0-18.647579-3.233684-18.647579-18.647579 0-5.066105-0.323368-10.401684 0-15.737263 0.269474-10.617263 4.742737-15.090526 15.090526-15.090527h5.658948c73.512421 0 146.701474 0 220.213895 0.323369 6.251789 0 8.299789-1.185684 8.299789-8.030316-0.269474-197.416421-0.269474-394.832842-0.269474-592.249263 0-7.114105-0.323368-14.228211-4.742737-20.48a23.821474 23.821474 0 0 0-20.48-10.617263H736.213962c-22.528 0-31.744 9.162105-31.744 31.690105v80.033684c0 12.773053-4.149895 16.922947-16.869053 17.192421-6.521263 0-13.365895 0.269474-19.887157-0.323368-8.623158-0.538947-12.719158-4.688842-12.719158-12.988632-0.323368-31.744-1.185684-63.757474 0.538947-95.447579A73.296842 73.296842 0 0 1 729.962173 14.228211c126.868211-0.323368 254.059789-0.323368 380.604631 0 43.870316 0.323368 74.698105 33.792 74.698105 80.033684 0.916211 126.868211 0.592842 253.736421 0.592843 380.604631z m-765.305263 457.943579H19.952909c-16.006737 0-19.563789-3.557053-19.563789-19.563789 0-5.012211-0.323368-10.347789 0-15.683369 0.538947-9.754947 5.012211-13.958737 14.821053-14.551579h804.75621c2.101895 0 4.149895-0.269474 6.25179 0 8.299789 0.592842 12.719158 4.742737 13.042526 13.042527 0.538947 8.030316 0.269474 16.006737 0 24.037052-0.323368 8.030316-4.742737 11.587368-12.449684 12.449685-2.964211 0.269474-5.658947 0.269474-8.623158 0.269473h-397.743158z", "p-id": "2493" }),
    react_1.default.createElement("path", { d: "M954.002594 813.056a33.792 33.792 0 0 1-32.87579 32.929684c-17.785263 0-32.875789-15.090526-33.199158-32.875789 0-18.108632 15.413895-33.253053 33.468632-33.253053 18.108632 0.323368 32.606316 15.144421 32.606316 33.199158z", "p-id": "2494" })));
