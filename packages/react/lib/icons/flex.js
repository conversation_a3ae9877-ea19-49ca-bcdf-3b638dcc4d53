"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlexWrap = exports.FlexNoWrap = exports.FlexAlignItemsBaseline = exports.FlexAlignItemsStretch = exports.FlexAlignItemsEnd = exports.FlexAlignItemsStart = exports.FlexAlignItemsCenter = exports.FlexJustifySpaceEvenly = exports.FlexJustifySpaceAround = exports.FlexJustifySpaceBetween = exports.FlexJustifyEnd = exports.FlexJustifyStart = exports.FlexJustifyCenter = exports.FlexAlignContentStretch = exports.FlexAlignContentSpaceBetween = exports.FlexAlignContentSpaceAround = exports.FlexAlignContentEnd = exports.FlexAlignContentStart = exports.FlexAlignContentCenter = exports.FlexDirectionColumn = exports.FlexDirectionRow = void 0;
var react_1 = __importDefault(require("react"));
exports.FlexDirectionRow = (react_1.default.createElement("path", { d: "M879.715178,837.944106 C885.731596,828.680733 898.118315,826.048555 907.381688,832.064973 L907.381688,832.064973 L1014.96809,901.940678 C1017.35919,903.493661 1019.39062,905.539937 1020.92614,907.942288 C1026.87492,917.249244 1024.15257,929.616459 1014.84562,935.565235 L1014.84562,935.565235 L907.259212,1004.3318 C904.042825,1006.38764 900.305277,1007.48007 896.488,1007.48007 C885.442305,1007.48007 876.488,998.525762 876.488,987.480067 L876.488,987.480067 L876.488,958 L40,958 C17.90861,958 2.705415e-15,940.09139 0,918 C-2.705415e-15,895.90861 17.90861,878 40,878 L876.488,878 L876.488,848.837795 C876.488,845.125618 877.521081,841.489854 879.46717,838.335822 Z M904,0 C970.27417,-1.21743675e-14 1024,53.72583 1024,120 L1024,680 C1024,746.27417 970.27417,800 904,800 L664,800 C597.72583,800 544,746.27417 544,680 L544,120 C544,53.72583 597.72583,1.21743675e-14 664,0 L904,0 Z M360,0 C426.27417,-1.21743675e-14 480,53.72583 480,120 L480,680 C480,746.27417 426.27417,800 360,800 L120,800 C53.72583,800 8.11624501e-15,746.27417 0,680 L0,120 C-8.11624501e-15,53.72583 53.72583,1.21743675e-14 120,0 L360,0 Z M320,80 L160,80 C116.259048,80 80.717181,115.104457 80.0107177,158.677054 L80,160 L80,640 C80,683.740952 115.104457,719.282819 158.677054,719.989282 L160,720 L320,720 C363.740952,720 399.282819,684.895543 399.989282,641.322946 L400,640 L400,160 C400,115.81722 364.18278,80 320,80 Z" }));
exports.FlexDirectionColumn = (react_1.default.createElement("path", { d: "M918,0 C940.09139,1.3527075e-15 958,17.90861 958,40 L958,876.487 L987.162205,876.488 C990.874382,876.488 994.510146,877.521081 997.664178,879.46717 L998.055894,879.715178 C1007.21634,885.664747 1009.89213,897.843841 1004.13237,907.071766 L1003.93503,907.381688 L934.059322,1014.96809 C932.506339,1017.35919 930.460063,1019.39062 928.057712,1020.92614 C918.85303,1026.80954 906.654978,1024.21136 900.633191,1015.15005 L900.434765,1014.84562 L831.668198,907.259212 C829.612363,904.042825 828.519933,900.305277 828.519933,896.488 C828.519933,885.557364 837.288662,876.674775 848.175444,876.490907 L848.519933,876.488 L878,876.487 L878,40 C878,17.90861 895.90861,-1.3527075e-15 918,0 Z M680,544 C746.27417,544 800,597.72583 800,664 L800,904 C800,970.27417 746.27417,1024 680,1024 L120,1024 C53.72583,1024 -1.05570593e-13,970.27417 -1.13686838e-13,904 L-1.13686838e-13,664 C-1.21803083e-13,597.72583 53.72583,544 120,544 L680,544 Z M680,1.36700294e-13 C746.27417,1.40758416e-13 800,53.72583 800,120 L800,360 C800,426.27417 746.27417,480 680,480 L120,480 C53.72583,480 -2.27373675e-13,426.27417 -2.27373675e-13,360 L-1.13686838e-13,120 C-1.13686838e-13,53.72583 53.72583,2.97718402e-14 120,3.38299627e-14 L680,1.36700294e-13 Z M640,80 L160,80 C116.259048,80 80.717181,115.104457 80.0107177,158.677054 L80,160 L80,320 C80,363.740952 115.104457,399.282819 158.677054,399.989282 L160,400 L640,400 C684.18278,400 720,364.18278 720,320 L720,160 C720,116.259048 684.895543,80.717181 641.322946,80.0107177 L640,80 Z" }));
exports.FlexAlignContentCenter = (react_1.default.createElement("path", { d: "M842,616 C908.27417,616 962,669.72583 962,736 L962,736 L962,816 C962,882.27417 908.27417,936 842,936 L842,936 L182,936 C115.72583,936 62,882.27417 62,816 L62,816 L62,736 C62,669.72583 115.72583,616 182,616 L182,616 Z M984,472 C1006.09139,472 1024,489.90861 1024,512 C1024,534.09139 1006.09139,552 984,552 L984,552 L40,552 C17.90861,552 -2.705415e-15,534.09139 0,512 C2.705415e-15,489.90861 17.90861,472 40,472 L40,472 Z M842,88 C908.27417,88 962,141.72583 962,208 L962,208 L962,288 C962,354.27417 908.27417,408 842,408 L842,408 L182,408 C115.72583,408 62,354.27417 62,288 L62,288 L62,208 C62,141.72583 115.72583,88 182,88 L182,88 Z" }));
exports.FlexAlignContentStart = (react_1.default.createElement("path", { d: "M842,584 C908.27417,584 962,637.72583 962,704 L962,704 L962,784 C962,850.27417 908.27417,904 842,904 L842,904 L182,904 C115.72583,904 62,850.27417 62,784 L62,784 L62,704 C62,637.72583 115.72583,584 182,584 L182,584 Z M40,200 C17.90861,200 -2.705415e-15,182.09139 0,160 C2.705415e-15,137.90861 17.90861,120 40,120 L40,120 L984,120 C1006.09139,120 1024,137.90861 1024,160 C1024,182.09139 1006.09139,200 984,200 L984,200 L962,200 L962,400 C962,466.27417 908.27417,520 842,520 L182,520 C115.72583,520 62,466.27417 62,400 L62,200 Z" }));
exports.FlexAlignContentEnd = (react_1.default.createElement("path", { d: "M62,624 C62,557.72583 115.72583,504 182,504 L182,504 L842,504 C908.27417,504 962,557.72583 962,624 L962,624 L962,824 L984,824 C1006.09139,824 1024,841.90861 1024,864 C1024,886.09139 1006.09139,904 984,904 L40,904 C17.90861,904 2.705415e-15,886.09139 0,864 C-2.705415e-15,841.90861 17.90861,824 40,824 L62,824 Z M842,120 C908.27417,120 962,173.72583 962,240 L962,320 C962,386.27417 908.27417,440 842,440 L182,440 C115.72583,440 62,386.27417 62,320 L62,240 C62,173.72583 115.72583,120 182,120 L842,120 Z" }));
exports.FlexAlignContentSpaceAround = (react_1.default.createElement("path", { d: "M984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 2.705415e-15,1006.09139 0,984 C-2.705415e-15,961.90861 17.90861,944 40,944 L984,944 Z M842,560 C908.27417,560 962,613.72583 962,680 L962,680 L962,760 C962,826.27417 908.27417,880 842,880 L842,880 L182,880 C115.72583,880 62,826.27417 62,760 L62,760 L62,680 C62,613.72583 115.72583,560 182,560 L182,560 Z M842,144 C908.27417,144 962,197.72583 962,264 L962,344 C962,410.27417 908.27417,464 842,464 L182,464 C115.72583,464 62,410.27417 62,344 L62,264 C62,197.72583 115.72583,144 182,144 L842,144 Z M984,0 C1006.09139,-4.05812251e-15 1024,17.90861 1024,40 C1024,62.09139 1006.09139,80 984,80 L40,80 C17.90861,80 2.705415e-15,62.09139 0,40 C-2.705415e-15,17.90861 17.90861,4.05812251e-15 40,0 L984,0 Z" }));
exports.FlexAlignContentSpaceBetween = (react_1.default.createElement("path", { d: "M62,744 C62,677.72583 115.72583,624 182,624 L182,624 L842,624 C908.27417,624 962,677.72583 962,744 L962,744 L962,944 L984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 2.705415e-15,1006.09139 0,984 C-2.705415e-15,961.90861 17.90861,944 40,944 L62,944 Z M984,0 C1006.09139,-4.05812251e-15 1024,17.90861 1024,40 C1024,62.09139 1006.09139,80 984,80 L962,80 L962,280 C962,346.27417 908.27417,400 842,400 L182,400 C115.72583,400 62,346.27417 62,280 L62,80 L40,80 C17.90861,80 2.705415e-15,62.09139 0,40 C-2.705415e-15,17.90861 17.90861,4.05812251e-15 40,0 L984,0 Z" }));
exports.FlexAlignContentStretch = (react_1.default.createElement("path", { d: "M62,664 C62,597.72583 115.72583,544 182,544 L182,544 L842,544 C908.27417,544 962,597.72583 962,664 L962,664 L962,944 L984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 2.705415e-15,1006.09139 0,984 C-2.705415e-15,961.90861 17.90861,944 40,944 L62,944 Z M984,0 C1006.09139,-4.05812251e-15 1024,17.90861 1024,40 C1024,62.09139 1006.09139,80 984,80 L962,80 L962,360 C962,426.27417 908.27417,480 842,480 L182,480 C115.72583,480 62,426.27417 62,360 L62,80 L40,80 C17.90861,80 2.705415e-15,62.09139 0,40 C-2.705415e-15,17.90861 17.90861,4.05812251e-15 40,0 L984,0 Z" }));
exports.FlexJustifyCenter = (react_1.default.createElement("path", { d: "M512,0 C534.09139,1.3527075e-15 552,17.90861 552,40 L552,40 L552,984 C552,1006.09139 534.09139,1024 512,1024 C489.90861,1024 472,1006.09139 472,984 L472,984 L472,40 C472,17.90861 489.90861,-1.3527075e-15 512,0 Z M288,242 C354.27417,242 408,295.72583 408,362 L408,362 L408,662 C408,728.27417 354.27417,782 288,782 L288,782 L208,782 C141.72583,782 88,728.27417 88,662 L88,662 L88,362 C88,295.72583 141.72583,242 208,242 L208,242 Z M816,242 C882.27417,242 936,295.72583 936,362 L936,362 L936,662 C936,728.27417 882.27417,782 816,782 L816,782 L736,782 C669.72583,782 616,728.27417 616,662 L616,662 L616,362 C616,295.72583 669.72583,242 736,242 L736,242 Z" }));
exports.FlexJustifyStart = (react_1.default.createElement("path", { d: "M160,0 C182.09139,1.3527075e-15 200,17.90861 200,40 L200,40 L200,984 C200,1006.09139 182.09139,1024 160,1024 C137.90861,1024 120,1006.09139 120,984 L120,984 L120,40 C120,17.90861 137.90861,-1.3527075e-15 160,0 Z M400,242 C466.27417,242 520,295.72583 520,362 L520,362 L520,662 C520,728.27417 466.27417,782 400,782 L400,782 L200,782 L200,242 Z M784,242 C850.27417,242 904,295.72583 904,362 L904,362 L904,662 C904,728.27417 850.27417,782 784,782 L784,782 L704,782 C637.72583,782 584,728.27417 584,662 L584,662 L584,362 C584,295.72583 637.72583,242 704,242 L704,242 Z" }));
exports.FlexJustifyEnd = (react_1.default.createElement("path", { d: "M864,0 C886.09139,-1.3527075e-15 904,17.90861 904,40 L904,984 C904,1006.09139 886.09139,1024 864,1024 C841.90861,1024 824,1006.09139 824,984 L824,782 L624,782 C557.72583,782 504,728.27417 504,662 L504,362 C504,295.72583 557.72583,242 624,242 L824,242 L824,40 C824,17.90861 841.90861,1.3527075e-15 864,0 Z M320,242 C386.27417,242 440,295.72583 440,362 L440,662 C440,728.27417 386.27417,782 320,782 L240,782 C173.72583,782 120,728.27417 120,662 L120,362 C120,295.72583 173.72583,242 240,242 L320,242 Z" }));
exports.FlexJustifySpaceBetween = (react_1.default.createElement("path", { d: "M984,0 C1006.09139,-1.3527075e-15 1024,17.90861 1024,40 L1024,984 C1024,1006.09139 1006.09139,1024 984,1024 C961.90861,1024 944,1006.09139 944,984 L944,782 L744,782 C677.72583,782 624,728.27417 624,662 L624,362 C624,295.72583 677.72583,242 744,242 L944,242 L944,40 C944,17.90861 961.90861,1.3527075e-15 984,0 Z M40,0 C62.09139,-1.3527075e-15 80,17.90861 80,40 L80,242 L280,242 C345.611428,242 398.924229,294.656686 399.983923,360.015581 L400,362 L400,662 C400,728.27417 346.27417,782 280,782 L280,782 L80,782 L80,512 L80,984 C80,1006.09139 62.09139,1024 40,1024 C17.90861,1024 -4.8316906e-13,1006.09139 -4.8316906e-13,984 L-4.26325641e-13,40 C-4.26325641e-13,17.90861 17.90861,1.3527075e-15 40,0 Z" }));
exports.FlexJustifySpaceAround = (react_1.default.createElement("path", { d: "M984,0 C1006.09139,-1.3527075e-15 1024,17.90861 1024,40 L1024,984 C1024,1006.09139 1006.09139,1024 984,1024 C961.90861,1024 944,1006.09139 944,984 L944,40 C944,17.90861 961.90861,1.3527075e-15 984,0 Z M40,0 C62.09139,-1.3527075e-15 80,17.90861 80,40 L80,984 C80,1006.09139 62.09139,1024 40,1024 C17.90861,1024 -2.84217094e-14,1006.09139 -2.84217094e-14,984 L2.84217094e-14,40 C2.84217094e-14,17.90861 17.90861,1.3527075e-15 40,0 Z M784,242 C850.27417,242 904,295.72583 904,362 L904,662 C904,728.27417 850.27417,782 784,782 L704,782 C637.72583,782 584,728.27417 584,662 L584,362 C584,295.72583 637.72583,242 704,242 L784,242 Z M320,242 C386.27417,242 440,295.72583 440,362 L440,362 L440,662 C440,728.27417 386.27417,782 320,782 L320,782 L240,782 C173.72583,782 120,728.27417 120,662 L120,662 L120,362 C120,295.72583 173.72583,242 240,242 L240,242 Z" }));
exports.FlexJustifySpaceEvenly = (react_1.default.createElement("path", { d: "M984,0 C1006.09139,-1.3527075e-15 1024,17.90861 1024,40 L1024,984 C1024,1006.09139 1006.09139,1024 984,1024 C961.90861,1024 944,1006.09139 944,984 L944,40 C944,17.90861 961.90861,1.3527075e-15 984,0 Z M40,0 C62.09139,-1.3527075e-15 80,17.90861 80,40 L80,984 C80,1006.09139 62.09139,1024 40,1024 C17.90861,1024 -2.84217094e-14,1006.09139 -2.84217094e-14,984 L2.84217094e-14,40 C2.84217094e-14,17.90861 17.90861,1.3527075e-15 40,0 Z M744,242 C810.27417,242 864,295.72583 864,362 L864,662 C864,728.27417 810.27417,782 744,782 L664,782 C597.72583,782 544,728.27417 544,662 L544,362 C544,295.72583 597.72583,242 664,242 L744,242 Z M360,242 C426.27417,242 480,295.72583 480,362 L480,362 L480,662 C480,728.27417 426.27417,782 360,782 L360,782 L280,782 C213.72583,782 160,728.27417 160,662 L160,662 L160,362 C160,295.72583 213.72583,242 280,242 L280,242 Z" }));
exports.FlexAlignItemsCenter = (react_1.default.createElement("path", { d: "M744,112 C810.27417,112 864,165.72583 864,232 L864,232 L864,471.999 L984,472 C1005.87048,472 1023.64141,489.552229 1023.99464,511.338527 L1024,512 C1024,534.09139 1006.09139,552 984,552 L984,552 L864,551.999 L864,792 C864,857.611428 811.343314,910.924229 745.984419,911.983923 L744,912 L664,912 C597.72583,912 544,858.27417 544,792 L544,792 L544,551.999 L479.999,552 L480,662 C480,727.611428 427.343314,780.924229 361.984419,781.983923 L360,782 L280,782 C213.72583,782 160,728.27417 160,662 L160,662 L159.999,552 L40,552 C18.1295239,552 0.358590478,534.447771 0.00535885717,512.661473 L6.82121026e-13,512 C6.82121026e-13,489.90861 17.90861,472 40,472 L40,472 L159.999,472 L160,362 C160,296.388572 212.656686,243.075771 278.015581,242.016077 L280,242 L360,242 C426.27417,242 480,295.72583 480,362 L480,362 L479.999,472 L544,471.999 L544,232 C544,166.388572 596.656686,113.075771 662.015581,112.016077 L664,112 Z" }));
exports.FlexAlignItemsStart = (react_1.default.createElement("path", { d: "M864,80 L864,760 C864,826.27417 810.27417,880 744,880 L744,880 L664,880 C597.72583,880 544,826.27417 544,760 L544,760 L544,80 L864,80 Z M480,80 L480,500 C480,566.27417 426.27417,620 360,620 L360,620 L280,620 C213.72583,620 160,566.27417 160,500 L160,500 L160,80 L480,80 Z M984,-1.15893678e-13 C1006.09139,-1.1454097e-13 1024,17.90861 1024,40 C1024,62.09139 1006.09139,80 984,80 L984,80 L40,80 C17.90861,80 6.82121026e-13,62.09139 6.82121026e-13,40 C6.82121026e-13,17.90861 17.90861,-1.63972755e-15 40,-2.87020043e-16 L40,-2.87020043e-16 Z" }));
exports.FlexAlignItemsEnd = (react_1.default.createElement("path", { d: "M160,524 C160,457.72583 213.72583,404 280,404 L360,404 C426.27417,404 480,457.72583 480,524 L480,944 L544,944 L544,264 C544,197.72583 597.72583,144 664,144 L744,144 C810.27417,144 864,197.72583 864,264 L864,944 L984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 9.09494702e-13,1006.09139 9.09494702e-13,984 C9.09494702e-13,961.90861 17.90861,944 40,944 L160,944 L160,524 Z" }));
exports.FlexAlignItemsStretch = (react_1.default.createElement("path", { d: "M480,80 L479.999,943.999 L544,944 L544,80 L864,80 L864,944 L984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 9.09494702e-13,1006.09139 9.09494702e-13,984 C9.09494702e-13,961.90861 17.90861,944 40,944 L159.999,943.999 L160,80 L480,80 Z M984,0 C1006.09139,0 1024,17.90861 1024,40 C1024,62.09139 1006.09139,80 984,80 L40,80 C17.90861,80 9.09494702e-13,62.09139 9.09494702e-13,40 C9.09494702e-13,17.90861 17.90861,-1.27897692e-13 40,-1.13686838e-13 L984,0 Z" }));
exports.FlexAlignItemsBaseline = (react_1.default.createElement("path", { d: "M40,944 L984,944 C1006.09139,944 1024,961.90861 1024,984 C1024,1006.09139 1006.09139,1024 984,1024 L40,1024 C17.90861,1024 9.09494702e-13,1006.09139 9.09494702e-13,984 C9.09494702e-13,961.90861 17.90861,944 40,944 Z M516.71049,0 C557.927273,0 586.19021,22.3748252 602.676923,58.8811189 L602.676923,58.8811189 L908.858741,741.902098 C914.746853,752.500699 917.102098,763.099301 917.102098,772.52028 C917.102098,811.381818 887.661538,842 848.8,842 C814.648951,842 791.096503,821.98042 778.142657,791.362238 L778.142657,791.362238 L711.018182,637.093706 L310.626573,637.093706 L241.146853,796.072727 C229.370629,825.513287 204.640559,842 174.022378,842 C136.338462,842 106.897902,812.559441 106.897902,774.875524 C106.897902,764.276923 110.430769,753.678322 116.318881,741.902098 L116.318881,741.902098 L422.500699,58.8811189 C438.987413,22.3748252 468.427972,0 509.644755,0 L509.644755,0 Z M510.822378,175.465734 L365.974825,508.732867 L655.66993,508.732867 L510.822378,175.465734 Z" }));
exports.FlexNoWrap = (react_1.default.createElement("path", { d: "M541,272 C607.27417,272 661,325.72583 661,392 L661,632 C661,698.27417 607.27417,752 541,752 L483,752 C416.72583,752 363,698.27417 363,632 L363,392 C363,325.72583 416.72583,272 483,272 L541,272 Z M178,272 C244.27417,272 298,325.72583 298,392 L298,632 C298,698.27417 244.27417,752 178,752 L120,752 C53.72583,752 8.11624501e-15,698.27417 0,632 L0,392 C-3.65379544e-14,325.72583 53.72583,272 120,272 L178,272 Z M904,272 C970.27417,272 1024,325.72583 1024,392 L1024,632 C1024,698.27417 970.27417,752 904,752 L846,752 C779.72583,752 726,698.27417 726,632 L726,392 C726,325.72583 779.72583,272 846,272 L904,272 Z M149,352 C111.273429,352 80.6185686,382.277594 80.009244,419.858959 L80,421 L80,603 C80,641.107648 110.892352,672 149,672 C186.726571,672 217.381431,641.722406 217.990756,604.141041 L218,603 L218,421 C218,382.892352 187.107648,352 149,352 Z M875,352 C837.273429,352 806.618569,382.277594 806.009244,419.858959 L806,421 L806,603 C806,641.107648 836.892352,672 875,672 C912.726571,672 943.381431,641.722406 943.990756,604.141041 L944,603 L944,421 C944,382.892352 913.107648,352 875,352 Z" }));
exports.FlexWrap = (react_1.default.createElement("path", { d: "M178,544 C244.27417,544 298,597.72583 298,664 L298,904 C298,970.27417 244.27417,1024 178,1024 L120,1024 C53.72583,1024 8.11624501e-15,970.27417 0,904 L0,664 C-3.65379544e-14,597.72583 53.72583,544 120,544 L178,544 Z M904,544 C970.27417,544 1024,597.72583 1024,664 L1024,904 C1024,970.27417 970.27417,1024 904,1024 L846,1024 C779.72583,1024 726,970.27417 726,904 L726,664 C726,597.72583 779.72583,544 846,544 L904,544 Z M541,544 C607.27417,544 661,597.72583 661,664 L661,904 C661,970.27417 607.27417,1024 541,1024 L483,1024 C416.72583,1024 363,970.27417 363,904 L363,664 C363,597.72583 416.72583,544 483,544 L541,544 Z M512,624 C474.273429,624 443.618569,654.277594 443.009244,691.858959 L443,693 L443,875 C443,913.107648 473.892352,944 512,944 C549.726571,944 580.381431,913.722406 580.990756,876.141041 L581,875 L581,693 C581,654.892352 550.107648,624 512,624 Z M541,0 C607.27417,-1.21743675e-14 661,53.72583 661,120 L661,360 C661,426.27417 607.27417,480 541,480 L483,480 C416.72583,480 363,426.27417 363,360 L363,120 C363,53.72583 416.72583,1.21743675e-14 483,0 L541,0 Z M178,0 C244.27417,-1.21743675e-14 298,53.72583 298,120 L298,360 C298,426.27417 244.27417,480 178,480 L120,480 C53.72583,480 8.11624501e-15,426.27417 0,360 L0,120 C-3.65379544e-14,53.72583 53.72583,1.21743675e-14 120,0 L178,0 Z M904,0 C970.27417,-1.21743675e-14 1024,53.72583 1024,120 L1024,360 C1024,426.27417 970.27417,480 904,480 L846,480 C779.72583,480 726,426.27417 726,360 L726,120 C726,53.72583 779.72583,1.21743675e-14 846,0 L904,0 Z M149,80 C111.273429,80 80.6185686,110.277594 80.009244,147.858959 L80,149 L80,331 C80,369.107648 110.892352,400 149,400 C186.726571,400 217.381431,369.722406 217.990756,332.141041 L218,331 L218,149 C218,110.892352 187.107648,80 149,80 Z M875,80 C837.273429,80 806.618569,110.277594 806.009244,147.858959 L806,149 L806,331 C806,369.107648 836.892352,400 875,400 C912.726571,400 943.381431,369.722406 943.990756,332.141041 L944,331 L944,149 C944,110.892352 913.107648,80 875,80 Z" }));
