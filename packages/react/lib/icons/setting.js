"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Setting = void 0;
var react_1 = __importDefault(require("react"));
exports.Setting = (react_1.default.createElement("path", { d: "M512,12 C582.636164,12 639.898089,69.261925 639.898089,139.892574 L639.898349,141.781694 C654.742486,146.909017 669.244536,152.924618 683.334385,159.790219 L684.678178,158.446609 C734.625489,108.499299 815.60608,108.499299 865.553391,158.446609 C914.944885,207.838104 915.554206,287.697419 867.027692,337.823433 L865.781597,339.091759 L864.209781,340.665615 C871.075392,354.755482 877.090999,369.257552 882.218326,384.101709 L884.101911,384.101911 C954.738075,384.101911 1012,441.363836 1012,512 C1012,581.850122 955.961792,638.750039 886.20392,639.881057 L884.266694,639.898067 L882.218306,639.898349 C877.090983,654.742486 871.075382,669.244536 864.209781,683.334385 L865.553391,684.678178 C915.500701,734.625489 915.500701,815.60608 865.553391,865.553391 C816.161896,914.944885 736.302581,915.554206 686.176567,867.027692 L684.908241,865.781597 L683.334385,864.209781 C669.244536,871.075382 654.742486,877.090983 639.898349,882.218306 L639.898089,884.101911 C639.898089,954.738075 582.636164,1012 512,1012 C442.149878,1012 385.249961,955.961792 384.118943,886.20392 L384.101928,884.266076 L384.101709,882.218326 C369.257552,877.090998 354.755482,871.075391 340.665615,864.209781 L339.321822,865.553391 C289.374511,915.500701 208.39392,915.500701 158.446609,865.553391 C108.499299,815.60608 108.499299,734.625489 158.444679,684.680108 L159.790219,683.334385 C152.924618,669.244536 146.909017,654.742486 141.781694,639.898349 L139.898089,639.898089 C69.261925,639.898089 12,582.636164 12,512 C12,441.363836 69.261925,384.101911 139.893809,384.101911 L141.781674,384.101709 C146.909002,369.257552 152.924609,354.755482 159.790219,340.665615 L158.446609,339.321822 C108.499299,289.374511 108.499299,208.39392 158.446609,158.446609 C208.39392,108.499299 289.374511,108.499299 339.319892,158.444679 L340.665615,159.790219 C354.115034,153.236682 367.940034,147.457631 382.079633,142.486357 L384.101709,141.781674 L384.101911,139.898089 C384.101911,69.261925 441.363836,12 512,12 Z M512,92 C485.546616,92 464.101911,113.444705 464.101911,139.902369 L464.095214,202.494122 L434.069257,210.22259 C405.791983,217.500958 378.786083,228.726636 353.744632,243.523454 L327.044529,259.300358 L282.753279,215.015152 C264.047912,196.309785 233.720519,196.309785 215.015152,215.015152 C196.309785,233.720519 196.309785,264.047912 215.017082,282.755209 L259.300358,327.044529 L243.523454,353.744632 C228.726636,378.786083 217.500958,405.791983 210.22259,434.069257 L202.494122,464.095214 L139.898089,464.101911 C113.444705,464.101911 92,485.546616 92,512 C92,538.453384 113.444705,559.898089 139.903605,559.89809 L202.494528,559.90672 L210.222812,589.931607 C217.501187,618.208568 228.726787,645.214173 243.523454,670.255368 L259.300358,696.955471 L215.015152,741.246721 C196.309785,759.952088 196.309785,790.279481 215.015152,808.984848 C233.720519,827.690215 264.047912,827.690215 282.755209,808.982918 L327.044529,764.699642 L353.744632,780.476546 C378.786083,795.273364 405.791983,806.499042 434.069257,813.77741 L464.095214,821.505878 L464.101893,883.937746 L464.110865,885.045091 C464.606855,911.08442 485.890111,932 512,932 C538.453384,932 559.898089,910.555295 559.89809,884.096395 L559.90672,821.505472 L589.931607,813.777188 C618.208568,806.498813 645.214173,795.273213 670.255368,780.476546 L696.955471,764.699642 L741.131179,808.869322 L741.919981,809.645446 C760.683285,827.707314 790.522368,827.447328 808.984848,808.984848 C827.690215,790.279481 827.690215,759.952088 808.982918,741.244791 L764.699642,696.955471 L780.476546,670.255368 C795.273213,645.214173 806.498813,618.208568 813.777188,589.931607 L821.505472,559.90672 L883.937128,559.898112 L885.045091,559.889135 C911.08442,559.393145 932,538.109889 932,512 C932,485.546616 910.555295,464.101911 884.097631,464.101911 L821.505878,464.095214 L813.77741,434.069257 C806.499042,405.791983 795.273364,378.786083 780.476546,353.744632 L764.699642,327.044529 L808.869322,282.868821 L809.645446,282.080019 L810.181805,281.51382 C827.700294,262.721071 827.262703,233.293007 808.984848,215.015152 C790.279481,196.309785 759.952088,196.309785 741.244791,215.017082 L696.955471,259.300358 L670.255368,243.523454 C645.214173,228.726787 618.208568,217.501187 589.931607,210.222812 L559.90672,202.494528 L559.898089,139.898089 C559.898089,113.444705 538.453384,92 512,92 Z M512,313 C621.904665,313 711,402.095335 711,512 C711,621.904665 621.904665,711 512,711 C402.095335,711 313,621.904665 313,512 C313,402.095335 402.095335,313 512,313 Z M512,393 C446.278115,393 393,446.278115 393,512 C393,577.721885 446.278115,631 512,631 C577.721885,631 631,577.721885 631,512 C631,446.278115 577.721885,393 512,393 Z" }));
