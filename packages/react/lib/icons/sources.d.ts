import React from 'react';
export declare const InputSource: React.JSX.Element;
export declare const TextAreaSource: React.JSX.Element;
export declare const SelectSource: React.JSX.Element;
export declare const TreeSelectSource: React.JSX.Element;
export declare const CascaderSource: React.JSX.Element;
export declare const RadioGroupSource: React.JSX.Element;
export declare const CheckboxGroupSource: React.JSX.Element;
export declare const SliderSource: React.JSX.Element;
export declare const RateSource: React.JSX.Element;
export declare const DatePickerSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const DateRangePickerSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const TimePickerSource: React.JSX.Element;
export declare const TimeRangePickerSource: React.JSX.Element;
export declare const NumberPickerSource: React.JSX.Element;
export declare const PasswordSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const TransferSource: React.JSX.Element;
export declare const UploadSource: React.JSX.Element;
export declare const UploadDraggerSource: React.JSX.Element;
export declare const SwitchSource: React.JSX.Element;
export declare const ObjectSource: React.JSX.Element;
export declare const CardSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const GridSource: React.JSX.Element;
export declare const FormLayoutSource: React.JSX.Element;
export declare const SpaceSource: React.JSX.Element;
export declare const TabSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const CollapseSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const ArrayCardsSource: React.JSX.Element;
export declare const ArrayTableSource: {
    light: React.JSX.Element;
    dark: React.JSX.Element;
};
export declare const ButtonSource: React.JSX.Element;
export declare const MediaSource: React.JSX.Element;
export declare const NotificationSource: React.JSX.Element;
export declare const ImageSource: React.JSX.Element;
export declare const TextSource: React.JSX.Element;
export declare const CreateButtonSource: React.JSX.Element;
export declare const DeleteButtonSource: React.JSX.Element;
export declare const SubmitButtonSource: React.JSX.Element;
export declare const ResetButtonSource: React.JSX.Element;
export declare const UpdateButtonSource: React.JSX.Element;
export declare const OpenPageButtonSource: React.JSX.Element;
export declare const CustomButtonSource: React.JSX.Element;
export declare const ConfirmButtonSource: React.JSX.Element;
export declare const CancelButtonSource: React.JSX.Element;
export declare const DetailLinkButtonSource: React.JSX.Element;
export declare const CreateLinkButtonSource: React.JSX.Element;
export declare const UpdateLinkButtonSource: React.JSX.Element;
export declare const DataTableSource: React.JSX.Element;
export declare const DataQueryListSource: React.JSX.Element;
export declare const DataFormSource: React.JSX.Element;
export declare const DataDetailFormSource: React.JSX.Element;
export declare const EntitySource: React.JSX.Element;
export declare const ImageEntitySource: React.JSX.Element;
export declare const FileEntitySource: React.JSX.Element;
export declare const FileSource: React.JSX.Element;
