"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Position = exports.Left = exports.Right = exports.Bottom = exports.Top = void 0;
var react_1 = __importDefault(require("react"));
exports.Top = (react_1.default.createElement("path", { d: "M539.414911,228.284271 L765.689081,454.558441 C781.310053,470.179413 781.310053,495.506012 765.689081,511.126984 C750.06811,526.747955 724.74151,526.747955 709.120539,511.126984 L551.13,353.137 L551.13064,904 C551.13064,926.09139 533.22203,944 511.13064,944 C489.03925,944 471.13064,926.09139 471.13064,904 L471.13,353.137 L313.140741,511.126984 C297.51977,526.747955 272.19317,526.747955 256.572199,511.126984 C240.951227,495.506012 240.951227,470.179413 256.572199,454.558441 L482.846369,228.284271 C498.46734,212.6633 523.79394,212.6633 539.414911,228.284271 Z M872,80 C894.09139,80 912,97.90861 912,120 C912,142.09139 894.09139,160 872,160 L152,160 C129.90861,160 112,142.09139 112,120 C112,97.90861 129.90861,80 152,80 L872,80 Z" }));
exports.Bottom = (react_1.default.createElement("path", { d: "M539.414911,228.284271 L765.689081,454.558441 C781.310053,470.179413 781.310053,495.506012 765.689081,511.126984 C750.06811,526.747955 724.74151,526.747955 709.120539,511.126984 L551.13,353.137 L551.13064,904 C551.13064,926.09139 533.22203,944 511.13064,944 C489.03925,944 471.13064,926.09139 471.13064,904 L471.13,353.137 L313.140741,511.126984 C297.51977,526.747955 272.19317,526.747955 256.572199,511.126984 C240.951227,495.506012 240.951227,470.179413 256.572199,454.558441 L482.846369,228.284271 C498.46734,212.6633 523.79394,212.6633 539.414911,228.284271 Z M872,80 C894.09139,80 912,97.90861 912,120 C912,142.09139 894.09139,160 872,160 L152,160 C129.90861,160 112,142.09139 112,120 C112,97.90861 129.90861,80 152,80 L872,80 Z", transform: "translate(512.000000, 512.000000) scale(1, -1) translate(-512.000000, -512.000000) " }));
exports.Right = (react_1.default.createElement("path", { d: "M539.414911,228.284271 L765.689081,454.558441 C781.310053,470.179413 781.310053,495.506012 765.689081,511.126984 C750.06811,526.747955 724.74151,526.747955 709.120539,511.126984 L551.13,353.137 L551.13064,904 C551.13064,926.09139 533.22203,944 511.13064,944 C489.03925,944 471.13064,926.09139 471.13064,904 L471.13,353.137 L313.140741,511.126984 C297.51977,526.747955 272.19317,526.747955 256.572199,511.126984 C240.951227,495.506012 240.951227,470.179413 256.572199,454.558441 L482.846369,228.284271 C498.46734,212.6633 523.79394,212.6633 539.414911,228.284271 Z M872,80 C894.09139,80 912,97.90861 912,120 C912,142.09139 894.09139,160 872,160 L152,160 C129.90861,160 112,142.09139 112,120 C112,97.90861 129.90861,80 152,80 L872,80 Z", transform: "translate(512.000000, 512.000000) rotate(-270.000000) translate(-512.000000, -512.000000) " }));
exports.Left = (react_1.default.createElement("path", { d: "M539.414911,228.284271 L765.689081,454.558441 C781.310053,470.179413 781.310053,495.506012 765.689081,511.126984 C750.06811,526.747955 724.74151,526.747955 709.120539,511.126984 L551.13,353.137 L551.13064,904 C551.13064,926.09139 533.22203,944 511.13064,944 C489.03925,944 471.13064,926.09139 471.13064,904 L471.13,353.137 L313.140741,511.126984 C297.51977,526.747955 272.19317,526.747955 256.572199,511.126984 C240.951227,495.506012 240.951227,470.179413 256.572199,454.558441 L482.846369,228.284271 C498.46734,212.6633 523.79394,212.6633 539.414911,228.284271 Z M872,80 C894.09139,80 912,97.90861 912,120 C912,142.09139 894.09139,160 872,160 L152,160 C129.90861,160 112,142.09139 112,120 C112,97.90861 129.90861,80 152,80 L872,80 Z", transform: "translate(512.000000, 512.000000) scale(-1, 1) rotate(-270.000000) translate(-512.000000, -512.000000) " }));
exports.Position = (react_1.default.createElement("path", { d: "M872,885 C893.879,885 911.64158,902.54379 911.994644,924.338274 L912,925 C912,947.1 894.1,965 872,965 L152,965 C130.121,965 112.35842,947.45621 112.005356,925.661726 L112,925 C112,902.9 129.9,885 152,885 L872,885 Z M512,60 C555.2,60 597.2,68.5 636.7,85.2 C674.8,101.3 709.1,124.4 738.5,153.8 C767.9,183.2 791,217.5 807.1,255.6 C823.8,295.1 832.3,337.1 832.3,380.3 C832.3,496.1 739.4,640.1 539.8,833.4 L512,860.3 L484.2,833.3 C284.6,640.1 191.7,496.1 191.7,380.3 C191.7,337.1 200.2,295.1 216.9,255.6 C233,217.5 256.1,183.2 285.5,153.8 C314.9,124.4 349.2,101.3 387.3,85.2 C426.8,68.5 468.8,60 512,60 Z M512,140 C447.8,140 387.5,165 342.1,210.4 C296.7,255.8 271.7,316.1 271.7,380.3 C271.7,424.4 293.5,479.4 336.5,543.8 C376.8,604 434.3,671.2 512,748.6 C589.6,671.2 647.2,604 687.5,543.8 C730.5,479.5 752.3,424.5 752.3,380.3 C752.3,316.1 727.3,255.8 681.9,210.4 C636.5,165 576.2,140 512,140 Z M512,228 C600.2,228 672,299.8 672,388 C672,476.2 600.2,548 512,548 C423.8,548 352,476.2 352,388 C352,299.8 423.8,228 512,228 Z M512,308 C467.9,308 432,343.9 432,388 C432,432.1 467.9,468 512,468 C556.1,468 592,432.1 592,388 C592,343.9 556.1,308 512,308 Z" }));
