"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DisplayFlex = exports.DisplayInline = exports.DisplayInlineBlock = exports.DisplayBlock = void 0;
var react_1 = __importDefault(require("react"));
exports.DisplayBlock = (react_1.default.createElement("path", { d: "M904,212 C970.27417,212 1024,265.72583 1024,332 L1024,692 C1024,758.27417 970.27417,812 904,812 L120,812 C53.72583,812 2.23270997e-14,758.27417 0,692 L0,332 C6.0946097e-15,265.72583 53.72583,212 120,212 L904,212 Z M864,292 L160,292 C115.81722,292 80,327.81722 80,372 L80,372 L80,652 C80,696.18278 115.81722,732 160,732 L160,732 L864,732 C908.18278,732 944,696.18278 944,652 L944,652 L944,372 C944,327.81722 908.18278,292 864,292 L864,292 Z" }));
exports.DisplayInlineBlock = (react_1.default.createElement("path", { d: "M904,212 C970.27417,212 1024,265.72583 1024,332 L1024,692 C1024,758.27417 970.27417,812 904,812 L120,812 C53.72583,812 2.23270997e-14,758.27417 0,692 L0,332 C6.0946097e-15,265.72583 53.72583,212 120,212 L904,212 Z M864,292 L160,292 C116.259048,292 80.717181,327.104457 80.0107177,370.677054 L80,372 L80,652 C80,695.740952 115.104457,731.282819 158.677054,731.989282 L160,732 L864,732 C907.740952,732 943.282819,696.895543 943.989282,653.322946 L944,652 L944,372 C944,327.81722 908.18278,292 864,292 Z M805.022222,357.5 C848.222222,357.5 876.622222,371.157459 901.022222,391.216851 C906.222222,395.484807 910.622222,402.740331 910.622222,412.129834 C910.622222,426.640884 899.822222,437.737569 886.222222,437.737569 C879.422222,437.737569 874.622222,435.176796 871.422222,432.616022 C852.222222,415.970994 831.422222,405.727901 804.622222,405.727901 C751.422222,405.727901 712.222222,452.675414 712.222222,511.146409 L712.222222,511.146409 L712.222222,512 C712.222222,570.470994 751.022222,617.845304 804.622222,617.845304 C834.222222,617.845304 854.222222,607.60221 874.622222,589.25 C878.222222,585.835635 883.422222,583.274862 889.422222,583.274862 C901.822222,583.274862 912.622222,594.371547 912.622222,607.60221 C912.622222,615.711326 909.022222,622.11326 904.622222,626.381215 C878.222222,651.135359 848.222222,666.5 803.022222,666.5 C721.422222,666.5 660.622222,598.639503 660.622222,512.853591 L660.622222,512.853591 L660.622222,512 C660.622222,427.06768 720.222222,357.5 805.022222,357.5 Z M250.577778,358.353591 C264.577778,358.353591 274.177778,366.462707 279.777778,379.69337 L279.777778,379.69337 L383.777778,627.234807 C385.777778,631.075967 386.577778,634.917127 386.577778,638.331492 C386.577778,652.415746 376.577778,663.512431 363.377778,663.512431 C351.777778,663.512431 343.777778,656.256906 339.377778,645.160221 L339.377778,645.160221 L316.577778,589.25 L180.577778,589.25 L156.977778,646.867403 C152.977778,657.537293 144.577778,663.512431 134.177778,663.512431 C121.377778,663.512431 111.377778,652.842541 111.377778,639.185083 C111.377778,635.343923 112.577778,631.502762 114.577778,627.234807 L114.577778,627.234807 L218.577778,379.69337 C224.177778,366.462707 234.177778,358.353591 248.177778,358.353591 L248.177778,358.353591 Z M537.6,362.621547 C569.6,362.621547 594.8,372.01105 610.8,389.082873 C623.2,402.313536 629.6,418.531768 629.6,438.59116 L629.6,438.59116 L629.6,439.444751 C629.6,475.29558 610.4,494.501381 589.6,506.024862 C622.4,517.975138 645.2,538.03453 645.2,578.58011 L645.2,578.58011 L645.2,579.433702 C645.2,632.783149 604,661.378453 541.6,661.378453 L541.6,661.378453 L436.8,661.378453 C422.8,661.378453 412,649.854972 412,634.917127 L412,634.917127 L412,389.082873 C412,374.145028 422.8,362.621547 436.8,362.621547 L436.8,362.621547 Z M538.4,532.912983 L460.4,532.912983 L460.4,614.857735 L542,614.857735 C575.6,614.857735 596,600.773481 596,573.885359 L596,573.885359 L596,573.031768 C596,547.850829 577.2,532.912983 538.4,532.912983 L538.4,532.912983 Z M248.577778,421.946133 L199.377778,542.729282 L297.777778,542.729282 L248.577778,421.946133 Z M531.2,409.142265 L460.4,409.142265 L460.4,488.526243 L527.6,488.526243 C559.2,488.526243 580.4,475.29558 580.4,447.980663 L580.4,447.980663 L580.4,447.127072 C580.4,423.653315 562.8,409.142265 531.2,409.142265 L531.2,409.142265 Z" }));
exports.DisplayInline = (react_1.default.createElement("path", { d: "M322.059019,705.634702 C338.928777,705.634702 351.708897,691.453006 351.708897,673.45316 C351.708897,669.089561 350.686488,664.180512 348.130464,659.271464 L215.217218,342.910542 C208.060351,326.001596 195.791436,315.638049 177.899268,315.638049 L174.832039,315.638049 C156.939871,315.638049 144.159751,326.001596 137.002884,342.910542 L4.08963834,659.271464 C1.53361438,664.725962 0,669.635011 0,674.54406 C0,691.998455 12.7801198,705.634702 29.1386732,705.634702 C42.4299978,705.634702 53.1652984,697.998404 58.2773464,684.362157 L88.4384291,610.726426 L262.248059,610.726426 L291.386732,682.180358 C297.009984,696.362054 307.23408,705.634702 322.059019,705.634702 Z M238.221433,551.27239 L112.465054,551.27239 L175.343244,396.910078 L238.221433,551.27239 Z M549.829155,702.907453 C629.577102,702.907453 682.231196,666.362312 682.231196,598.181079 L682.231196,597.090179 C682.231196,545.272442 653.092523,519.636298 611.17373,504.363702 C637.756379,489.636556 662.294209,465.091312 662.294209,419.273523 L662.294209,418.182623 C662.294209,392.54648 654.114932,371.819385 638.267584,354.910439 C617.819392,333.092444 585.61349,321.092547 544.717107,321.092547 L415.893499,321.092547 C398.001331,321.092547 384.198802,335.819694 384.198802,354.910439 L384.198802,669.089561 C384.198802,688.180306 398.001331,702.907453 415.893499,702.907453 L549.829155,702.907453 Z M531.936987,482.000257 L446.054582,482.000257 L446.054582,380.546583 L536.53783,380.546583 C576.923009,380.546583 599.41602,399.091878 599.41602,429.091621 L599.41602,430.18252 C599.41602,465.091312 572.322166,482.000257 531.936987,482.000257 Z M550.340359,643.453417 L446.054582,643.453417 L446.054582,538.727043 L545.739516,538.727043 C595.326381,538.727043 619.353006,557.817789 619.353006,589.999331 L619.353006,591.09023 C619.353006,625.453572 593.281562,643.453417 550.340359,643.453417 Z M883.929887,709.452851 C941.696028,709.452851 980.036388,689.816656 1013.7759,658.180564 C1019.39916,652.726065 1024,644.544317 1024,634.18077 C1024,617.271824 1010.19747,603.090127 994.350122,603.090127 C986.68205,603.090127 980.036388,606.362827 975.435545,610.726426 C949.3641,634.18077 923.803861,647.271567 885.974706,647.271567 C817.473264,647.271567 767.886399,586.726632 767.886399,512 L767.886399,510.9091 C767.886399,436.182469 817.984469,376.182984 885.974706,376.182984 C920.225427,376.182984 946.808076,389.27378 971.345906,410.546325 C975.435545,413.819024 981.570002,417.091723 990.260484,417.091723 C1007.64145,417.091723 1021.44398,402.910027 1021.44398,384.364732 C1021.44398,372.364835 1015.82072,363.092187 1009.17506,357.637688 C977.991569,332.001545 941.696028,314.547149 886.485911,314.547149 C778.110495,314.547149 701.940981,403.455477 701.940981,512 L701.940981,513.0909 C701.940981,622.726323 779.644109,709.452851 883.929887,709.452851 Z" }));
exports.DisplayFlex = (react_1.default.createElement("path", { d: "M904,212 C970.27417,212 1024,265.72583 1024,332 L1024,692 C1024,758.27417 970.27417,812 904,812 L120,812 C53.72583,812 2.23270997e-14,758.27417 0,692 L0,332 C6.0946097e-15,265.72583 53.72583,212 120,212 L904,212 Z M864,292 L160,292 C116.259048,292 80.717181,327.104457 80.0107177,370.677054 L80,372 L80,652 C80,695.740952 115.104457,731.282819 158.677054,731.989282 L160,732 L864,732 C907.740952,732 943.282819,696.895543 943.989282,653.322946 L944,652 L944,372 C944,327.81722 908.18278,292 864,292 Z M250,382 C294.18278,382 330,417.81722 330,462 L330,562 C330,606.18278 294.18278,642 250,642 C205.81722,642 170,606.18278 170,562 L170,462 C170,417.81722 205.81722,382 250,382 Z M512,382 C556.18278,382 592,417.81722 592,462 L592,562 C592,606.18278 556.18278,642 512,642 C467.81722,642 432,606.18278 432,562 L432,462 C432,417.81722 467.81722,382 512,382 Z M774,382 C818.18278,382 854,417.81722 854,462 L854,562 C854,606.18278 818.18278,642 774,642 C729.81722,642 694,606.18278 694,562 L694,462 C694,417.81722 729.81722,382 774,382 Z" }));
