declare const _default: {
    'zh-CN': {
        icons: {
            Image: string;
            ImageSize: string;
            Position: string;
            Number: string;
            Text: string;
            Expression: string;
            RichText: string;
            Boolean: string;
            Repeat: string;
            Top: string;
            Left: string;
            Right: string;
            Bottom: string;
            TopLeft: string;
            TopRight: string;
            BottomRight: string;
            BottomLeft: string;
            DisplayBlock: string;
            DisplayInlineBlock: string;
            DisplayInline: string;
            DisplayFlex: string;
            FlexDirectionRow: string;
            FlexDirectionColumn: string;
            FlexAlignContentCenter: string;
            FlexAlignContentStart: string;
            FlexAlignContentEnd: string;
            FlexAlignContentSpaceAround: string;
            FlexAlignContentSpaceBetween: string;
            FlexAlignContentStretch: string;
            FlexJustifyCenter: string;
            FlexJustifyStart: string;
            FlexJustifyEnd: string;
            FlexJustifySpaceBetween: string;
            FlexJustifySpaceAround: string;
            FlexJustifySpaceEvenly: string;
            FlexAlignItemsCenter: string;
            FlexAlignItemsStart: string;
            FlexAlignItemsEnd: string;
            FlexAlignItemsStretch: string;
            FlexAlignItemsBaseline: string;
            FlexNoWrap: string;
            FlexWrap: string;
            AxisX: string;
            AxisY: string;
            Blur: string;
            Shadow: string;
            FontWeight: string;
            FontStyle: string;
            NormalFontStyle: string;
            ItalicFontStyle: string;
            FontColor: string;
            FontSize: string;
            LineHeight: string;
            TextDecoration: string;
            TextUnderline: string;
            TextLineThrough: string;
            TextAlign: string;
            TextAlignLeft: string;
            TextAlignCenter: string;
            TextAlignRight: string;
            TextAlignJustify: string;
        };
    };
    'en-US': {
        icons: {
            Image: string;
            ImageSize: string;
            Boolean: string;
            Number: string;
            Text: string;
            Expression: string;
            RichText: string;
            Position: string;
            Repeat: string;
            Top: string;
            Left: string;
            Right: string;
            Bottom: string;
            TopLeft: string;
            TopRight: string;
            BottomRight: string;
            BottomLeft: string;
            DisplayBlock: string;
            DisplayInlineBlock: string;
            DisplayInline: string;
            DisplayFlex: string;
            FlexDirectionRow: string;
            FlexDirectionColumn: string;
            FlexAlignContentCenter: string;
            FlexAlignContentStart: string;
            FlexAlignContentEnd: string;
            FlexAlignContentSpaceAround: string;
            FlexAlignContentSpaceBetween: string;
            FlexAlignContentStretch: string;
            FlexJustifyCenter: string;
            FlexJustifyStart: string;
            FlexJustifyEnd: string;
            FlexJustifySpaceBetween: string;
            FlexJustifySpaceAround: string;
            FlexJustifySpaceEvenly: string;
            FlexAlignItemsCenter: string;
            FlexAlignItemsStart: string;
            FlexAlignItemsEnd: string;
            FlexAlignItemsStretch: string;
            FlexAlignItemsBaseline: string;
            FlexNoWrap: string;
            FlexWrap: string;
            AxisX: string;
            AxisY: string;
            Blur: string;
            Shadow: string;
            FontWeight: string;
            FontStyle: string;
            NormalFontStyle: string;
            ItalicFontStyle: string;
            FontColor: string;
            FontSize: string;
            LineHeight: string;
            TextDecoration: string;
            TextUnderline: string;
            TextLineThrough: string;
            TextAlign: string;
            TextAlignLeft: string;
            TextAlignCenter: string;
            TextAlignRight: string;
            TextAlignJustify: string;
        };
    };
};
export default _default;
