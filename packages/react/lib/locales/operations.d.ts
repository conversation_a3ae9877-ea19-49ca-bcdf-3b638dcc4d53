declare const _default: {
    'zh-CN': {
        operations: {
            default_state: string;
            append_node: string;
            prepend_node: string;
            clone_node: string;
            update_node_props: string;
            insert_after: string;
            insert_before: string;
            insert_children: string;
            update_children: string;
            remove_node: string;
            wrap_node: string;
            from_node: string;
        };
    };
    'en-US': {
        operations: {
            default_state: string;
            append_node: string;
            prepend_node: string;
            clone_node: string;
            update_node_props: string;
            insert_after: string;
            insert_before: string;
            insert_children: string;
            update_children: string;
            remove_node: string;
            wrap_node: string;
            from_node: string;
        };
    };
    'ko-KR': {
        operations: {
            default_state: string;
            append_node: string;
            prepend_node: string;
            clone_node: string;
            update_node_props: string;
            insert_after: string;
            insert_before: string;
            insert_children: string;
            update_children: string;
            remove_node: string;
            wrap_node: string;
            from_node: string;
        };
    };
};
export default _default;
