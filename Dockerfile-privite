# 定义构建参数（buildx 传入，Jenkins 可传入）
ARG YARN_VERSION=1.22.22
ARG NODE_ENV=production
ARG BUILD_NUMBER
ARG GIT_COMMIT
ARG GIT_BRANCH
ARG YARN_REGISTRY
ARG YARN_USERNAME
ARG YARN_PASSWORD
ARG YARN_EMAIL

# 第一阶段：构建
FROM node:22-alpine AS builder

# 重新声明构建参数
ARG YARN_VERSION
ARG NODE_ENV
ARG BUILD_NUMBER
ARG GIT_COMMIT
ARG GIT_BRANCH
ARG YARN_REGISTRY
ARG YARN_USERNAME
ARG YARN_PASSWORD
ARG YARN_EMAIL

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=${NODE_ENV}
ENV YARN_CACHE_FOLDER=/app/.yarn-cache

# 安装依赖
RUN apk add --no-cache git curl

# 创建非 root 用户（适配 Jenkins 权限）
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 配置私有 registry 认证
RUN echo "//${YARN_REGISTRY#http://}:_auth=$(echo -n ${YARN_USERNAME}:${YARN_PASSWORD} | base64 | tr -d '\n')" > /app/.npmrc && \
    echo "email=${YARN_EMAIL}" >> /app/.npmrc && \
    echo "registry=${YARN_REGISTRY}" >> /app/.npmrc && \
    chown nodejs:nodejs /app/.npmrc

# 复制根目录的依赖文件
COPY --chown=nodejs:nodejs package.json yarn.lock lerna-minimal.json ./

# 复制所有包的 package.json 文件
COPY --chown=nodejs:nodejs packages/shared/package.json ./packages/shared/
COPY --chown=nodejs:nodejs packages/core/package.json ./packages/core/
COPY --chown=nodejs:nodejs packages/react/package.json ./packages/react/
COPY --chown=nodejs:nodejs packages/react-settings-form/package.json ./packages/react-settings-form/
COPY --chown=nodejs:nodejs formily/transformer/package.json ./formily/transformer/
COPY --chown=nodejs:nodejs formily/setters/package.json ./formily/setters/
COPY --chown=nodejs:nodejs formily/antd/package.json ./formily/antd/

# 切换到非 root 用户
USER nodejs

# 配置 yarn 缓存和网络
RUN yarn config set cache-folder /app/.yarn-cache && \
    yarn config set network-timeout 300000

# 安装依赖（利用 Docker 缓存层）
RUN yarn install --frozen-lockfile --network-timeout 300000 --verbose

# 复制所有源代码
COPY --chown=nodejs:nodejs . .

# 构建项目
RUN echo "开始构建项目..." && \
    echo "构建环境: $NODE_ENV" && \
    echo "构建编号: $BUILD_NUMBER" && \
    echo "Git 提交: $GIT_COMMIT" && \
    echo "Git 分支: $GIT_BRANCH" && \
    echo "使用私有 registry: $YARN_REGISTRY" && \
    yarn build && \
    rm -f /app/.npmrc && \
    echo "构建完成，检查构建产物..." && \
    ls -la /app/formily/antd/dist && \
    find /app/formily/antd/dist -type f && \
    echo "构建产物大小:" && \
    du -sh /app/formily/antd/dist

# 第二阶段：生产环境
FROM nginx:1.28-alpine

# 安装 curl 用于健康检查
RUN apk add --no-cache curl

# 创建非 root 用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 拷贝 nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 拷贝构建产物
COPY --from=builder --chown=nginx:nginx /app/formily/antd/dist /usr/share/nginx/html

# 设置构建信息（用于版本追踪）
ARG BUILD_NUMBER
ARG GIT_COMMIT
ARG GIT_BRANCH
RUN echo "Build Number: ${BUILD_NUMBER}" > /usr/share/nginx/html/build-info.txt && \
    echo "Git Commit: ${GIT_COMMIT}" >> /usr/share/nginx/html/build-info.txt && \
    echo "Git Branch: ${GIT_BRANCH}" >> /usr/share/nginx/html/build-info.txt && \
    echo "Build Time: $(date)" >> /usr/share/nginx/html/build-info.txt && \
    echo "Registry: Private" >> /usr/share/nginx/html/build-info.txt

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# 切换到非 root 用户
USER nginx

# 启动服务
CMD ["nginx", "-g", "daemon off;"]
