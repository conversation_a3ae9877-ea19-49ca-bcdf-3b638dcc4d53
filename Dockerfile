# 定义构建参数
ARG YARN_VERSION=1.22.22

# 第一阶段：构建阶段
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置字符编码环境变量
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 安装依赖
RUN apk add --no-cache git

# 复制依赖文件并安装
COPY package.json yarn.lock lerna-minimal.json ./
RUN yarn install --frozen-lockfile --network-timeout 1000000 || yarn install --network-timeout 1000000

# 复制项目文件
COPY . .

# 运行 bootstrap 来设置 workspaces
RUN yarn bootstrap

# 构建项目
RUN yarn build && \
  rm -rf node_modules && \
  ls -la /app/dist && \
  find /app/dist -type f

# 第二阶段：生产镜像
FROM nginx:1.28-alpine

# 安装 wget 用于健康检查
RUN apk add --no-cache wget

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 从构建阶段复制产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# 启动nginx服务
CMD ["nginx", "-g", "daemon off;"]
